{"name": "chorenet-backend", "version": "1.0.0", "description": "Backend API for ChoreNet WiFi Rewards System", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "vitest", "db:migrate": "tsx src/scripts/migrate.ts", "db:seed": "tsx src/scripts/seed.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "dotenv": "^16.3.1", "zod": "^3.22.4", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "ws": "^8.14.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/pg": "^8.10.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/node-cron": "^3.0.11", "@types/ws": "^8.5.10", "tsx": "^4.6.2", "typescript": "^5.3.3", "vitest": "^1.0.4"}}
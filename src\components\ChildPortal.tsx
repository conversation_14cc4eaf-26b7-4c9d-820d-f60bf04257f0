import React, { useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useData } from '../contexts/DataContext';
import { 
  Clock, 
  CheckCircle, 
  Upload, 
  Camera, 
  Star,
  Trophy,
  Calendar,
  LogOut,
  Plus,
  Gift
} from 'lucide-react';

export const ChildPortal: React.FC = () => {
  const { user, logout } = useAuth();
  const { childUsers, tasks, submissions, submitTask } = useData();
  const [activeTab, setActiveTab] = useState<'home' | 'tasks' | 'history'>('home');
  const [selectedTask, setSelectedTask] = useState<string | null>(null);
  const [photoFile, setPhotoFile] = useState<File | null>(null);
  const [note, setNote] = useState('');
  const [showSubmitModal, setShowSubmitModal] = useState(false);

  const currentChild = childUsers.find(c => c.id === user?.id);
  const mySubmissions = submissions.filter(s => s.childId === user?.id);
  const recentSubmissions = mySubmissions.slice(-3);

  const handleTaskSubmit = () => {
    if (selectedTask && user?.id) {
      const photoUrl = photoFile ? 'https://images.pexels.com/photos/1454806/pexels-photo-1454806.jpeg?auto=compress&cs=tinysrgb&w=400' : undefined;
      submitTask(selectedTask, user.id, photoUrl, note);
      setSelectedTask(null);
      setPhotoFile(null);
      setNote('');
      setShowSubmitModal(false);
    }
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (!currentChild) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-2 rounded-lg">
                <Gift className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Hi, {user?.name}!</h1>
                <p className="text-sm text-gray-600">Earn time by completing tasks</p>
              </div>
            </div>
            <button
              onClick={logout}
              className="flex items-center space-x-1 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <LogOut className="w-4 h-4" />
              <span>Logout</span>
            </button>
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex space-x-1 mb-8">
          {[
            { id: 'home', label: 'Home' },
            { id: 'tasks', label: 'Tasks' },
            { id: 'history', label: 'History' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {activeTab === 'home' && (
          <div className="space-y-8">
            <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold mb-2">Time Remaining</h2>
                  <p className="text-3xl font-bold">{formatTime(currentChild.timeBalance)}</p>
                </div>
                <div className="text-right">
                  <Clock className="w-16 h-16 opacity-20" />
                </div>
              </div>
              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <p className="text-sm opacity-80">Tasks Done</p>
                    <p className="text-xl font-bold">{currentChild.tasksCompleted}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm opacity-80">Total Earned</p>
                    <p className="text-xl font-bold">{formatTime(currentChild.totalTimeEarned)}</p>
                  </div>
                </div>
                <div className={`w-4 h-4 rounded-full ${currentChild.isOnline ? 'bg-green-400' : 'bg-gray-400'}`} />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Trophy className="w-5 h-5 text-yellow-500 mr-2" />
                  Quick Tasks
                </h3>
                <div className="space-y-3">
                  {tasks.slice(0, 3).map((task) => (
                    <div key={task.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{task.title}</p>
                        <p className="text-sm text-gray-600">{task.rewardMinutes} minutes</p>
                      </div>
                      <button
                        onClick={() => {
                          setSelectedTask(task.id);
                          setShowSubmitModal(true);
                        }}
                        className="bg-purple-600 text-white px-3 py-1 rounded-lg hover:bg-purple-700 transition-colors"
                      >
                        Do It!
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Calendar className="w-5 h-5 text-blue-500 mr-2" />
                  Recent Activity
                </h3>
                <div className="space-y-3">
                  {recentSubmissions.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">No recent activity</p>
                  ) : (
                    recentSubmissions.map((submission) => (
                      <div key={submission.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium text-gray-900">{submission.taskTitle}</p>
                          <p className="text-sm text-gray-600">{submission.rewardMinutes} minutes</p>
                        </div>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(submission.status)}`}>
                          {submission.status}
                        </span>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'tasks' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Available Tasks</h2>
              <div className="text-sm text-gray-600">
                Complete tasks to earn WiFi time!
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {tasks.map((task) => (
                <div key={task.id} className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">{task.title}</h3>
                      <p className="text-gray-600 text-sm mb-3">{task.description}</p>
                      <div className="flex items-center space-x-2 mb-3">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getDifficultyColor(task.difficulty)}`}>
                          {task.difficulty}
                        </span>
                        <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                          {task.category}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1 text-green-600">
                      <Star className="w-4 h-4" />
                      <span className="font-bold">{task.rewardMinutes}m</span>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      setSelectedTask(task.id);
                      setShowSubmitModal(true);
                    }}
                    className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors flex items-center justify-center space-x-2"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Submit Task</span>
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'history' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Task History</h2>
            
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              {mySubmissions.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  <Trophy className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                  <p>No tasks submitted yet</p>
                  <p className="text-sm">Complete your first task to start earning time!</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {mySubmissions.map((submission) => (
                    <div key={submission.id} className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 mb-1">{submission.taskTitle}</h3>
                          <p className="text-gray-600 text-sm mb-2">
                            Submitted on {submission.submittedAt.toLocaleDateString()}
                          </p>
                          {submission.note && (
                            <p className="text-gray-700 text-sm mb-2">{submission.note}</p>
                          )}
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(submission.status)}`}>
                              {submission.status}
                            </span>
                            <span className="text-sm text-gray-600">
                              {submission.rewardMinutes} minutes
                            </span>
                          </div>
                        </div>
                        {submission.photoUrl && (
                          <img 
                            src={submission.photoUrl} 
                            alt="Task proof" 
                            className="w-20 h-20 object-cover rounded-lg ml-4"
                          />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {showSubmitModal && selectedTask && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Submit Task</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Add a note (optional)
                </label>
                <textarea
                  value={note}
                  onChange={(e) => setNote(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  rows={3}
                  placeholder="Tell us about what you did..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Add photo proof (optional)
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => setPhotoFile(e.target.files?.[0] || null)}
                    className="hidden"
                    id="photo-upload"
                  />
                  <label
                    htmlFor="photo-upload"
                    className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 cursor-pointer"
                  >
                    <Camera className="w-4 h-4" />
                    <span className="text-sm">Choose Photo</span>
                  </label>
                  {photoFile && (
                    <span className="text-sm text-gray-600">{photoFile.name}</span>
                  )}
                </div>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowSubmitModal(false)}
                className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleTaskSubmit}
                className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors flex items-center justify-center space-x-2"
              >
                <Upload className="w-4 h-4" />
                <span>Submit</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
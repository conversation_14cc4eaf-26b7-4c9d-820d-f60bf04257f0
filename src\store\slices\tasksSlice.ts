import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

export interface Task {
  taskId: string;
  name: string;
  description: string;
  minutesReward: number;
  category: string;
  difficultyLevel: number;
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface TaskCompletion {
  completionId: string;
  taskId: string;
  userId: string;
  completedAt: string;
  proofUrl?: string;
  proofType?: string;
  notes?: string;
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: string;
  approvedAt?: string;
  rejectionReason?: string;
  minutesEarned: number;
  // Populated fields
  taskName?: string;
  userName?: string;
}

interface TasksState {
  tasks: Task[];
  completions: TaskCompletion[];
  loading: boolean;
  error: string | null;
}

const initialState: TasksState = {
  tasks: [],
  completions: [],
  loading: false,
  error: null,
};

// Helper function to get auth headers
const getAuthHeaders = (getState: any) => {
  const token = getState().auth.token;
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// Async thunks
export const fetchTasks = createAsyncThunk(
  'tasks/fetchTasks',
  async (_, { getState, rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/tasks`, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch tasks');
    }
  }
);

export const createTask = createAsyncThunk(
  'tasks/createTask',
  async (taskData: {
    name: string;
    description: string;
    minutesReward: number;
    category: string;
    difficultyLevel: number;
  }, { getState, rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/tasks`, taskData, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create task');
    }
  }
);

export const updateTask = createAsyncThunk(
  'tasks/updateTask',
  async ({ taskId, ...taskData }: {
    taskId: string;
    name?: string;
    description?: string;
    minutesReward?: number;
    category?: string;
    difficultyLevel?: number;
    isActive?: boolean;
  }, { getState, rejectWithValue }) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/tasks/${taskId}`, taskData, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update task');
    }
  }
);

export const deleteTask = createAsyncThunk(
  'tasks/deleteTask',
  async (taskId: string, { getState, rejectWithValue }) => {
    try {
      await axios.delete(`${API_BASE_URL}/tasks/${taskId}`, {
        headers: getAuthHeaders(getState),
      });
      return taskId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete task');
    }
  }
);

export const fetchTaskCompletions = createAsyncThunk(
  'tasks/fetchTaskCompletions',
  async (params?: { status?: string; userId?: string }, { getState, rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams();
      if (params?.status) queryParams.append('status', params.status);
      if (params?.userId) queryParams.append('userId', params.userId);

      const response = await axios.get(`${API_BASE_URL}/tasks/completions?${queryParams}`, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch task completions');
    }
  }
);

export const submitTaskCompletion = createAsyncThunk(
  'tasks/submitTaskCompletion',
  async (completionData: {
    taskId: string;
    proofUrl?: string;
    notes?: string;
  }, { getState, rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/tasks/completions`, completionData, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to submit task completion');
    }
  }
);

export const approveTaskCompletion = createAsyncThunk(
  'tasks/approveTaskCompletion',
  async (completionId: string, { getState, rejectWithValue }) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/tasks/completions/${completionId}/approve`, {}, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to approve task completion');
    }
  }
);

export const rejectTaskCompletion = createAsyncThunk(
  'tasks/rejectTaskCompletion',
  async ({ completionId, reason }: { completionId: string; reason?: string }, { getState, rejectWithValue }) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/tasks/completions/${completionId}/reject`, {
        rejectionReason: reason,
      }, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to reject task completion');
    }
  }
);

const tasksSlice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch tasks
      .addCase(fetchTasks.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTasks.fulfilled, (state, action) => {
        state.loading = false;
        state.tasks = action.payload;
      })
      .addCase(fetchTasks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Create task
      .addCase(createTask.fulfilled, (state, action) => {
        state.tasks.push(action.payload);
      })
      
      // Update task
      .addCase(updateTask.fulfilled, (state, action) => {
        const index = state.tasks.findIndex(task => task.taskId === action.payload.taskId);
        if (index !== -1) {
          state.tasks[index] = action.payload;
        }
      })
      
      // Delete task
      .addCase(deleteTask.fulfilled, (state, action) => {
        state.tasks = state.tasks.filter(task => task.taskId !== action.payload);
      })
      
      // Fetch task completions
      .addCase(fetchTaskCompletions.fulfilled, (state, action) => {
        state.completions = action.payload;
      })
      
      // Submit task completion
      .addCase(submitTaskCompletion.fulfilled, (state, action) => {
        state.completions.push(action.payload);
      })
      
      // Approve task completion
      .addCase(approveTaskCompletion.fulfilled, (state, action) => {
        const index = state.completions.findIndex(completion => completion.completionId === action.payload.completionId);
        if (index !== -1) {
          state.completions[index] = action.payload;
        }
      })
      
      // Reject task completion
      .addCase(rejectTaskCompletion.fulfilled, (state, action) => {
        const index = state.completions.findIndex(completion => completion.completionId === action.payload.completionId);
        if (index !== -1) {
          state.completions[index] = action.payload;
        }
      });
  },
});

export const { clearError } = tasksSlice.actions;
export default tasksSlice.reducer;

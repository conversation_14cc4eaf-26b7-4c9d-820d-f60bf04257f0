# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/chorenet
DB_HOST=localhost
DB_PORT=5432
DB_NAME=chorenet
DB_USER=chorenet_user
DB_PASSWORD=your_secure_password

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here
JWT_EXPIRES_IN=24h

# Server Configuration
PORT=3001
NODE_ENV=development

# Router Configuration
ROUTER_HOST=***********
ROUTER_USERNAME=admin
ROUTER_PASSWORD=admin_password
ROUTER_API_PORT=80

# Network Configuration
KIDS_NETWORK_SUBNET=***********/24
MAIN_NETWORK_SUBNET=***********/24

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880

# Logging
LOG_LEVEL=info

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

export interface Device {
  deviceId: string;
  userId: string;
  macAddress: string;
  name: string;
  deviceType: string;
  isActive: boolean;
  lastSeen?: string;
  createdAt: string;
  updatedAt: string;
  // Populated fields
  userName?: string;
  isConnected?: boolean;
}

interface DevicesState {
  devices: Device[];
  loading: boolean;
  error: string | null;
}

const initialState: DevicesState = {
  devices: [],
  loading: false,
  error: null,
};

// Helper function to get auth headers
const getAuthHeaders = (getState: any) => {
  const token = getState().auth.token;
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// Async thunks
export const fetchDevices = createAsyncThunk(
  'devices/fetchDevices',
  async (userId?: string, { getState, rejectWithValue }) => {
    try {
      const url = userId 
        ? `${API_BASE_URL}/devices/user/${userId}`
        : `${API_BASE_URL}/devices`;
        
      const response = await axios.get(url, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch devices');
    }
  }
);

export const createDevice = createAsyncThunk(
  'devices/createDevice',
  async (deviceData: {
    userId: string;
    macAddress: string;
    name: string;
    deviceType: string;
  }, { getState, rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/devices`, deviceData, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create device');
    }
  }
);

export const updateDevice = createAsyncThunk(
  'devices/updateDevice',
  async ({ deviceId, ...deviceData }: {
    deviceId: string;
    name?: string;
    deviceType?: string;
    isActive?: boolean;
  }, { getState, rejectWithValue }) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/devices/${deviceId}`, deviceData, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update device');
    }
  }
);

export const deleteDevice = createAsyncThunk(
  'devices/deleteDevice',
  async (deviceId: string, { getState, rejectWithValue }) => {
    try {
      await axios.delete(`${API_BASE_URL}/devices/${deviceId}`, {
        headers: getAuthHeaders(getState),
      });
      return deviceId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete device');
    }
  }
);

export const scanForDevices = createAsyncThunk(
  'devices/scanForDevices',
  async (_, { getState, rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/devices/scan`, {}, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to scan for devices');
    }
  }
);

const devicesSlice = createSlice({
  name: 'devices',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateDeviceStatus: (state, action) => {
      const { deviceId, isConnected, lastSeen } = action.payload;
      const device = state.devices.find(d => d.deviceId === deviceId);
      if (device) {
        device.isConnected = isConnected;
        if (lastSeen) device.lastSeen = lastSeen;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch devices
      .addCase(fetchDevices.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDevices.fulfilled, (state, action) => {
        state.loading = false;
        state.devices = action.payload;
      })
      .addCase(fetchDevices.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Create device
      .addCase(createDevice.fulfilled, (state, action) => {
        state.devices.push(action.payload);
      })
      
      // Update device
      .addCase(updateDevice.fulfilled, (state, action) => {
        const index = state.devices.findIndex(device => device.deviceId === action.payload.deviceId);
        if (index !== -1) {
          state.devices[index] = action.payload;
        }
      })
      
      // Delete device
      .addCase(deleteDevice.fulfilled, (state, action) => {
        state.devices = state.devices.filter(device => device.deviceId !== action.payload);
      })
      
      // Scan for devices
      .addCase(scanForDevices.fulfilled, (state, action) => {
        // Add newly discovered devices
        const existingMacs = new Set(state.devices.map(d => d.macAddress));
        const newDevices = action.payload.filter((device: Device) => !existingMacs.has(device.macAddress));
        state.devices.push(...newDevices);
      });
  },
});

export const { clearError, updateDeviceStatus } = devicesSlice.actions;
export default devicesSlice.reducer;

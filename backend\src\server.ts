import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import cron from 'node-cron';

// Import routes
import authRoutes from './routes/auth.js';
import userRoutes from './routes/users.js';
import taskRoutes from './routes/tasks.js';
import deviceRoutes from './routes/devices.js';
import networkRoutes from './routes/network.js';
import timeRoutes from './routes/time.js';

// Import middleware
import { errorHandler } from './middleware/errorHandler.js';
import { requestLogger } from './middleware/requestLogger.js';
import { rateLimiter } from './middleware/rateLimiter.js';

// Import services
import { DatabaseService } from './services/DatabaseService.js';
import { NetworkService } from './services/NetworkService.js';
import { WebSocketService } from './services/WebSocketService.js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Create HTTP server for WebSocket support
const server = createServer(app);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://your-domain.com'] 
    : ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true,
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging
app.use(requestLogger);

// Rate limiting
app.use(rateLimiter);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/devices', deviceRoutes);
app.use('/api/network', networkRoutes);
app.use('/api/time', timeRoutes);

// Static file serving for uploads
app.use('/uploads', express.static('uploads'));

// Error handling middleware (must be last)
app.use(errorHandler);

// Initialize services
async function initializeServices() {
  try {
    // Initialize database
    await DatabaseService.initialize();
    console.log('✅ Database service initialized');

    // Initialize network service
    await NetworkService.initialize();
    console.log('✅ Network service initialized');

    // Initialize WebSocket service
    const wss = new WebSocketServer({ server });
    WebSocketService.initialize(wss);
    console.log('✅ WebSocket service initialized');

  } catch (error) {
    console.error('❌ Failed to initialize services:', error);
    process.exit(1);
  }
}

// Schedule daily reset of usage counters (runs at midnight)
cron.schedule('0 0 * * *', async () => {
  try {
    await DatabaseService.resetDailyUsage();
    console.log('✅ Daily usage counters reset');
  } catch (error) {
    console.error('❌ Failed to reset daily usage:', error);
  }
});

// Schedule cleanup of expired sessions (runs every 5 minutes)
cron.schedule('*/5 * * * *', async () => {
  try {
    await NetworkService.cleanupExpiredSessions();
    console.log('✅ Expired sessions cleaned up');
  } catch (error) {
    console.error('❌ Failed to cleanup expired sessions:', error);
  }
});

// Graceful shutdown handling
process.on('SIGTERM', async () => {
  console.log('🔄 SIGTERM received, shutting down gracefully...');
  
  server.close(() => {
    console.log('✅ HTTP server closed');
    DatabaseService.close();
    process.exit(0);
  });
});

process.on('SIGINT', async () => {
  console.log('🔄 SIGINT received, shutting down gracefully...');
  
  server.close(() => {
    console.log('✅ HTTP server closed');
    DatabaseService.close();
    process.exit(0);
  });
});

// Start server
async function startServer() {
  await initializeServices();
  
  server.listen(PORT, () => {
    console.log(`🚀 ChoreNet Backend Server running on port ${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
  });
}

startServer().catch((error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

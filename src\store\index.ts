import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import tasksSlice from './slices/tasksSlice';
import usersSlice from './slices/usersSlice';
import devicesSlice from './slices/devicesSlice';
import networkSlice from './slices/networkSlice';
import timeSlice from './slices/timeSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    tasks: tasksSlice,
    users: usersSlice,
    devices: devicesSlice,
    network: networkSlice,
    time: timeSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

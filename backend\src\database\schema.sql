-- ChoreNet Database Schema
-- PostgreSQL Database Schema for WiFi Rewards System

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (parents and children)
CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('parent', 'child')),
    parent_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Create index for email lookups
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_parent_id ON users(parent_id);

-- Devices table (child devices for network control)
CREATE TABLE devices (
    device_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    mac_address VARCHAR(17) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    device_type VARCHAR(50) DEFAULT 'unknown',
    is_active BOOLEAN DEFAULT true,
    last_seen TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index for MAC address lookups (critical for network control)
CREATE INDEX idx_devices_mac ON devices(mac_address);
CREATE INDEX idx_devices_user_id ON devices(user_id);

-- Tasks table (available chores/tasks)
CREATE TABLE tasks (
    task_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    minutes_reward INTEGER NOT NULL CHECK (minutes_reward > 0),
    category VARCHAR(100) DEFAULT 'general',
    difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level BETWEEN 1 AND 5),
    is_active BOOLEAN DEFAULT true,
    created_by UUID NOT NULL REFERENCES users(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_tasks_created_by ON tasks(created_by);
CREATE INDEX idx_tasks_active ON tasks(is_active);

-- Task completions table (track completed tasks and earned time)
CREATE TABLE task_completions (
    completion_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID NOT NULL REFERENCES tasks(task_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    proof_url VARCHAR(255),
    proof_type VARCHAR(50),
    notes TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    approved_by UUID REFERENCES users(user_id),
    approved_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    minutes_earned INTEGER DEFAULT 0
);

-- Critical index for querying task history by user and date
CREATE INDEX idx_task_completions_user_date ON task_completions(user_id, completed_at);
CREATE INDEX idx_task_completions_status ON task_completions(status);
CREATE INDEX idx_task_completions_approved_by ON task_completions(approved_by);

-- Time banks table (track available internet time per child)
CREATE TABLE time_banks (
    user_id UUID PRIMARY KEY REFERENCES users(user_id) ON DELETE CASCADE,
    minutes_available INTEGER NOT NULL DEFAULT 0 CHECK (minutes_available >= 0),
    minutes_used_today INTEGER DEFAULT 0 CHECK (minutes_used_today >= 0),
    last_reset_date DATE DEFAULT CURRENT_DATE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Network sessions table (track active internet sessions)
CREATE TABLE network_sessions (
    session_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    device_id UUID NOT NULL REFERENCES devices(device_id) ON DELETE CASCADE,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER DEFAULT 0,
    bytes_downloaded BIGINT DEFAULT 0,
    bytes_uploaded BIGINT DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'terminated'))
);

CREATE INDEX idx_network_sessions_user_id ON network_sessions(user_id);
CREATE INDEX idx_network_sessions_device_id ON network_sessions(device_id);
CREATE INDEX idx_network_sessions_status ON network_sessions(status);
CREATE INDEX idx_network_sessions_started_at ON network_sessions(started_at);

-- Create a view for daily usage summary
CREATE VIEW daily_usage_summary AS
SELECT 
    u.user_id,
    u.first_name,
    u.last_name,
    DATE(tc.completed_at) as date,
    COUNT(tc.completion_id) as tasks_completed,
    SUM(tc.minutes_earned) as total_minutes_earned,
    COALESCE(ns.total_minutes_used, 0) as total_minutes_used
FROM users u
LEFT JOIN task_completions tc ON u.user_id = tc.user_id AND tc.status = 'approved'
LEFT JOIN (
    SELECT 
        user_id,
        DATE(started_at) as date,
        SUM(duration_minutes) as total_minutes_used
    FROM network_sessions 
    WHERE status IN ('expired', 'terminated')
    GROUP BY user_id, DATE(started_at)
) ns ON u.user_id = ns.user_id AND DATE(tc.completed_at) = ns.date
WHERE u.role = 'child'
GROUP BY u.user_id, u.first_name, u.last_name, DATE(tc.completed_at), ns.total_minutes_used;

-- Function to update time bank when task is approved
CREATE OR REPLACE FUNCTION update_time_bank_on_approval()
RETURNS TRIGGER AS $$
BEGIN
    -- Only process if status changed to approved
    IF NEW.status = 'approved' AND (OLD.status IS NULL OR OLD.status != 'approved') THEN
        -- Get the minutes reward for the task
        SELECT minutes_reward INTO NEW.minutes_earned
        FROM tasks 
        WHERE task_id = NEW.task_id;
        
        -- Update the time bank
        INSERT INTO time_banks (user_id, minutes_available)
        VALUES (NEW.user_id, NEW.minutes_earned)
        ON CONFLICT (user_id) 
        DO UPDATE SET 
            minutes_available = time_banks.minutes_available + NEW.minutes_earned,
            updated_at = CURRENT_TIMESTAMP;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic time bank updates
CREATE TRIGGER trigger_update_time_bank
    AFTER UPDATE ON task_completions
    FOR EACH ROW
    EXECUTE FUNCTION update_time_bank_on_approval();

-- Function to reset daily usage counters
CREATE OR REPLACE FUNCTION reset_daily_usage()
RETURNS void AS $$
BEGIN
    UPDATE time_banks 
    SET 
        minutes_used_today = 0,
        last_reset_date = CURRENT_DATE
    WHERE last_reset_date < CURRENT_DATE;
END;
$$ LANGUAGE plpgsql;

import { Pool, PoolClient } from 'pg';
import fs from 'fs/promises';
import path from 'path';

export class DatabaseService {
  private static pool: Pool;
  private static isInitialized = false;

  static async initialize(): Promise<void> {
    if (this.isInitialized) return;

    const config = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      database: process.env.DB_NAME || 'chorenet',
      user: process.env.DB_USER || 'chorenet_user',
      password: process.env.DB_PASSWORD,
      max: 20, // Maximum number of clients in the pool
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    };

    this.pool = new Pool(config);

    // Test the connection
    try {
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();
      this.isInitialized = true;
      console.log('✅ Database connection established');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw error;
    }
  }

  static async getClient(): Promise<PoolClient> {
    if (!this.isInitialized) {
      throw new Error('Database service not initialized');
    }
    return this.pool.connect();
  }

  static async query(text: string, params?: any[]): Promise<any> {
    if (!this.isInitialized) {
      throw new Error('Database service not initialized');
    }
    
    const start = Date.now();
    try {
      const result = await this.pool.query(text, params);
      const duration = Date.now() - start;
      
      if (duration > 1000) {
        console.warn(`⚠️ Slow query detected (${duration}ms):`, text.substring(0, 100));
      }
      
      return result;
    } catch (error) {
      console.error('❌ Database query error:', error);
      console.error('Query:', text);
      console.error('Params:', params);
      throw error;
    }
  }

  static async transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
    const client = await this.getClient();
    
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // User management methods
  static async createUser(userData: {
    email: string;
    passwordHash: string;
    firstName: string;
    lastName: string;
    role: 'parent' | 'child';
    parentId?: string;
  }) {
    const query = `
      INSERT INTO users (email, password_hash, first_name, last_name, role, parent_id)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING user_id, email, first_name, last_name, role, created_at
    `;
    
    const result = await this.query(query, [
      userData.email,
      userData.passwordHash,
      userData.firstName,
      userData.lastName,
      userData.role,
      userData.parentId || null
    ]);
    
    return result.rows[0];
  }

  static async getUserByEmail(email: string) {
    const query = `
      SELECT user_id, email, password_hash, first_name, last_name, role, parent_id, is_active
      FROM users 
      WHERE email = $1 AND is_active = true
    `;
    
    const result = await this.query(query, [email]);
    return result.rows[0];
  }

  static async getUserById(userId: string) {
    const query = `
      SELECT user_id, email, first_name, last_name, role, parent_id, is_active, created_at
      FROM users 
      WHERE user_id = $1 AND is_active = true
    `;
    
    const result = await this.query(query, [userId]);
    return result.rows[0];
  }

  static async getChildrenByParentId(parentId: string) {
    const query = `
      SELECT user_id, email, first_name, last_name, role, created_at
      FROM users 
      WHERE parent_id = $1 AND is_active = true
      ORDER BY first_name, last_name
    `;
    
    const result = await this.query(query, [parentId]);
    return result.rows;
  }

  // Device management methods
  static async createDevice(deviceData: {
    userId: string;
    macAddress: string;
    name: string;
    deviceType?: string;
  }) {
    const query = `
      INSERT INTO devices (user_id, mac_address, name, device_type)
      VALUES ($1, $2, $3, $4)
      RETURNING device_id, user_id, mac_address, name, device_type, created_at
    `;
    
    const result = await this.query(query, [
      deviceData.userId,
      deviceData.macAddress,
      deviceData.name,
      deviceData.deviceType || 'unknown'
    ]);
    
    return result.rows[0];
  }

  static async getDevicesByUserId(userId: string) {
    const query = `
      SELECT device_id, mac_address, name, device_type, is_active, last_seen, created_at
      FROM devices 
      WHERE user_id = $1 AND is_active = true
      ORDER BY name
    `;
    
    const result = await this.query(query, [userId]);
    return result.rows;
  }

  static async getDeviceByMacAddress(macAddress: string) {
    const query = `
      SELECT d.device_id, d.user_id, d.mac_address, d.name, d.device_type, d.is_active,
             u.first_name, u.last_name, u.role
      FROM devices d
      JOIN users u ON d.user_id = u.user_id
      WHERE d.mac_address = $1 AND d.is_active = true AND u.is_active = true
    `;
    
    const result = await this.query(query, [macAddress]);
    return result.rows[0];
  }

  // Time bank methods
  static async getTimeBank(userId: string) {
    const query = `
      SELECT user_id, minutes_available, minutes_used_today, last_reset_date, updated_at
      FROM time_banks 
      WHERE user_id = $1
    `;
    
    const result = await this.query(query, [userId]);
    return result.rows[0];
  }

  static async updateTimeBank(userId: string, minutesUsed: number) {
    const query = `
      UPDATE time_banks 
      SET 
        minutes_available = GREATEST(0, minutes_available - $2),
        minutes_used_today = minutes_used_today + $2,
        updated_at = CURRENT_TIMESTAMP
      WHERE user_id = $1
      RETURNING minutes_available, minutes_used_today
    `;
    
    const result = await this.query(query, [userId, minutesUsed]);
    return result.rows[0];
  }

  // Reset daily usage counters
  static async resetDailyUsage(): Promise<void> {
    await this.query('SELECT reset_daily_usage()');
  }

  static async close(): Promise<void> {
    if (this.pool) {
      await this.pool.end();
      this.isInitialized = false;
      console.log('✅ Database connection pool closed');
    }
  }

  // Migration helper
  static async runMigrations(): Promise<void> {
    try {
      const schemaPath = path.join(process.cwd(), 'src', 'database', 'schema.sql');
      const schema = await fs.readFile(schemaPath, 'utf-8');
      await this.query(schema);
      console.log('✅ Database migrations completed');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }
}

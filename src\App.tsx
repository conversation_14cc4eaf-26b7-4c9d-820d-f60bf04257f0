import React, { useState, useEffect } from 'react';
import { Provider } from 'react-redux';
import { ParentDashboard } from './components/ParentDashboard';
import { ChildPortal } from './components/ChildPortal';
import { CaptivePortal } from './components/CaptivePortal';
import { LoginForm } from './components/LoginForm';
import { store } from './store';
import { useAppDispatch, useAppSelector } from './hooks/redux';
import { verifyToken } from './store/slices/authSlice';

function AppContent() {
  const dispatch = useAppDispatch();
  const { user, isAuthenticated, loading } = useAppSelector(state => state.auth);
  const [showCaptivePortal, setShowCaptivePortal] = useState(false);

  useEffect(() => {
    // Try to verify existing token on app load
    const token = localStorage.getItem('token');
    if (token && !isAuthenticated) {
      dispatch(verifyToken());
    }
  }, [dispatch, isAuthenticated]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading ChoreNet...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginForm />;
  }

  // Show captive portal for children trying to access internet
  if (user?.role === 'child' && showCaptivePortal) {
    return <CaptivePortal />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {user?.role === 'parent' ? (
        <ParentDashboard />
      ) : (
        <div>
          <div className="fixed top-4 right-4 z-50">
            <button
              onClick={() => setShowCaptivePortal(!showCaptivePortal)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              {showCaptivePortal ? 'Back to Portal' : 'Internet Access'}
            </button>
          </div>
          {showCaptivePortal ? <CaptivePortal /> : <ChildPortal />}
        </div>
      )}
    </div>
  );
}

function App() {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
}

export default App;
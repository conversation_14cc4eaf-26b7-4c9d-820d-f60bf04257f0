import React, { useState } from 'react';
import { ParentDashboard } from './components/ParentDashboard';
import { ChildPortal } from './components/ChildPortal';
import { CaptivePortal } from './components/CaptivePortal';
import { LoginForm } from './components/LoginForm';
import { useAuth } from './hooks/useAuth';
import { AuthProvider } from './contexts/AuthContext';
import { DataProvider } from './contexts/DataContext';

function AppContent() {
  const { user, isAuthenticated } = useAuth();
  const [showCaptivePortal, setShowCaptivePortal] = useState(false);

  if (!isAuthenticated) {
    return <LoginForm />;
  }

  // Show captive portal for children trying to access internet
  if (user?.role === 'child' && showCaptivePortal) {
    return <CaptivePortal />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {user?.role === 'parent' ? (
        <ParentDashboard />
      ) : (
        <div>
          <div className="fixed top-4 right-4 z-50">
            <button
              onClick={() => setShowCaptivePortal(!showCaptivePortal)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              {showCaptivePortal ? 'Back to Portal' : 'Internet Access'}
            </button>
          </div>
          {showCaptivePortal ? <CaptivePortal /> : <ChildPortal />}
        </div>
      )}
    </div>
  );
}

function App() {
  return (
    <AuthProvider>
      <DataProvider>
        <AppContent />
      </DataProvider>
    </AuthProvider>
  );
}

export default App;
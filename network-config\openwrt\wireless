# OpenWRT Wireless Configuration for ChoreNet
# File: /etc/config/wireless

config wifi-device 'radio0'
	option type 'mac80211'
	option channel '11'
	option hwmode '11g'
	option path 'platform/10180000.wmac'
	option htmode 'HT20'
	option disabled '0'
	option country 'US'

config wifi-device 'radio1'
	option type 'mac80211'
	option channel '36'
	option hwmode '11a'
	option path 'pci0000:00/0000:00:00.0'
	option htmode 'VHT80'
	option disabled '0'
	option country 'US'

# Main Network (Adults/Unrestricted)
config wifi-iface 'default_radio0'
	option device 'radio0'
	option network 'lan'
	option mode 'ap'
	option ssid 'FamilyWiFi'
	option encryption 'psk2'
	option key 'your_main_wifi_password'
	option hidden '0'

config wifi-iface 'default_radio1'
	option device 'radio1'
	option network 'lan'
	option mode 'ap'
	option ssid 'FamilyWiFi_5G'
	option encryption 'psk2'
	option key 'your_main_wifi_password'
	option hidden '0'

# Kids Network (Restricted)
config wifi-iface 'kids_radio0'
	option device 'radio0'
	option network 'lan_kids'
	option mode 'ap'
	option ssid 'KidsWiFi'
	option encryption 'psk2'
	option key 'your_kids_wifi_password'
	option hidden '0'
	option isolate '1'

config wifi-iface 'kids_radio1'
	option device 'radio1'
	option network 'lan_kids'
	option mode 'ap'
	option ssid 'KidsWiFi_5G'
	option encryption 'psk2'
	option key 'your_kids_wifi_password'
	option hidden '0'
	option isolate '1'

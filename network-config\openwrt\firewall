# OpenWRT Firewall Configuration for ChoreNet
# File: /etc/config/firewall

config defaults
	option syn_flood '1'
	option input 'ACCEPT'
	option output 'ACCEPT'
	option forward 'REJECT'

config zone
	option name 'lan'
	list network 'lan'
	option input 'ACCEPT'
	option output 'ACCEPT'
	option forward 'ACCEPT'

config zone
	option name 'wan'
	list network 'wan'
	list network 'wan6'
	option input 'REJECT'
	option output 'ACCEPT'
	option forward 'REJECT'
	option masq '1'
	option mtu_fix '1'

# Kids Network Zone (Restricted)
config zone
	option name 'kids'
	list network 'lan_kids'
	option input 'ACCEPT'
	option output 'ACCEPT'
	option forward 'REJECT'

# Allow LAN to WAN
config forwarding
	option src 'lan'
	option dest 'wan'

# Block Kids to WAN by default (will be overridden by specific rules)
config rule
	option name 'Block Kids Internet Default'
	option src 'kids'
	option dest 'wan'
	option target 'REJECT'
	option enabled '1'

# Allow Kids to LAN (for local services)
config forwarding
	option src 'kids'
	option dest 'lan'

# Allow LAN to Kids (for management)
config forwarding
	option src 'lan'
	option dest 'kids'

# Allow SSH from LAN
config rule
	option name 'Allow-SSH'
	option src 'lan'
	option proto 'tcp'
	option dest_port '22'
	option target 'ACCEPT'

# Allow HTTP/HTTPS to router from LAN
config rule
	option name 'Allow-HTTP'
	option src 'lan'
	option proto 'tcp'
	option dest_port '80'
	option target 'ACCEPT'

config rule
	option name 'Allow-HTTPS'
	option src 'lan'
	option proto 'tcp'
	option dest_port '443'
	option target 'ACCEPT'

# Allow DHCP
config rule
	option name 'Allow-DHCP-Renew'
	option src 'wan'
	option proto 'udp'
	option dest_port '68'
	option target 'ACCEPT'
	option family 'ipv4'

# Allow Ping
config rule
	option name 'Allow-Ping'
	option src 'wan'
	option proto 'icmp'
	option icmp_type 'echo-request'
	option family 'ipv4'
	option target 'ACCEPT'

# Allow DHCPv6 replies
config rule
	option name 'Allow-DHCPv6'
	option src 'wan'
	option proto 'udp'
	option src_ip 'fc00::/6'
	option dest_ip 'fc00::/6'
	option dest_port '546'
	option family 'ipv6'
	option target 'ACCEPT'

# Allow essential IPv6 ICMP traffic
config rule
	option name 'Allow-MLD'
	option src 'wan'
	option proto 'icmp'
	option src_ip 'fe80::/10'
	list icmp_type '130/0'
	list icmp_type '131/0'
	list icmp_type '132/0'
	list icmp_type '143/0'
	option family 'ipv6'
	option target 'ACCEPT'

# Allow ICMPv6 input
config rule
	option name 'Allow-ICMPv6-Input'
	option src 'wan'
	option proto 'icmp'
	list icmp_type 'echo-request'
	list icmp_type 'echo-reply'
	list icmp_type 'destination-unreachable'
	list icmp_type 'packet-too-big'
	list icmp_type 'time-exceeded'
	list icmp_type 'bad-header'
	list icmp_type 'unknown-header-type'
	list icmp_type 'router-solicitation'
	list icmp_type 'neighbour-solicitation'
	list icmp_type 'router-advertisement'
	list icmp_type 'neighbour-advertisement'
	option limit '1000/sec'
	option family 'ipv6'
	option target 'ACCEPT'

# Allow ICMPv6 forward
config rule
	option name 'Allow-ICMPv6-Forward'
	option src 'wan'
	option dest '*'
	option proto 'icmp'
	list icmp_type 'echo-request'
	list icmp_type 'echo-reply'
	list icmp_type 'destination-unreachable'
	list icmp_type 'packet-too-big'
	list icmp_type 'time-exceeded'
	list icmp_type 'bad-header'
	list icmp_type 'unknown-header-type'
	option limit '1000/sec'
	option family 'ipv6'
	option target 'ACCEPT'

# Allow IPSec ESP traffic
config rule
	option name 'Allow-IPSec-ESP'
	option src 'wan'
	option dest 'lan'
	option proto 'esp'
	option target 'ACCEPT'

# Allow ISAKMP
config rule
	option name 'Allow-ISAKMP'
	option src 'wan'
	option dest 'lan'
	option dest_port '500'
	option proto 'udp'
	option target 'ACCEPT'

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

export interface TimeBank {
  userId: string;
  minutesAvailable: number;
  minutesUsedToday: number;
  lastResetDate: string;
  updatedAt: string;
  // Populated fields
  userName?: string;
}

export interface UsageStats {
  totalMinutesEarned: number;
  totalMinutesUsed: number;
  averageDailyUsage: number;
  streakDays: number;
  tasksCompletedThisWeek: number;
}

interface TimeState {
  timeBanks: TimeBank[];
  usageStats: { [userId: string]: UsageStats };
  loading: boolean;
  error: string | null;
}

const initialState: TimeState = {
  timeBanks: [],
  usageStats: {},
  loading: false,
  error: null,
};

// Helper function to get auth headers
const getAuthHeaders = (getState: any) => {
  const token = getState().auth.token;
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// Async thunks
export const fetchTimeBanks = createAsyncThunk(
  'time/fetchTimeBanks',
  async (_, { getState, rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/time/banks`, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch time banks');
    }
  }
);

export const fetchTimeBank = createAsyncThunk(
  'time/fetchTimeBank',
  async (userId: string, { getState, rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/time/banks/${userId}`, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch time bank');
    }
  }
);

export const addTime = createAsyncThunk(
  'time/addTime',
  async ({ userId, minutes, reason }: {
    userId: string;
    minutes: number;
    reason?: string;
  }, { getState, rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/time/add`, {
        userId,
        minutes,
        reason,
      }, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to add time');
    }
  }
);

export const deductTime = createAsyncThunk(
  'time/deductTime',
  async ({ userId, minutes, reason }: {
    userId: string;
    minutes: number;
    reason?: string;
  }, { getState, rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/time/deduct`, {
        userId,
        minutes,
        reason,
      }, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to deduct time');
    }
  }
);

export const fetchUsageStats = createAsyncThunk(
  'time/fetchUsageStats',
  async (userId: string, { getState, rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/time/stats/${userId}`, {
        headers: getAuthHeaders(getState),
      });
      return { userId, stats: response.data };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch usage stats');
    }
  }
);

export const resetDailyUsage = createAsyncThunk(
  'time/resetDailyUsage',
  async (_, { getState, rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/time/reset-daily`, {}, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to reset daily usage');
    }
  }
);

const timeSlice = createSlice({
  name: 'time',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateTimeBank: (state, action) => {
      const { userId, minutesAvailable, minutesUsedToday } = action.payload;
      const bank = state.timeBanks.find(b => b.userId === userId);
      if (bank) {
        bank.minutesAvailable = minutesAvailable;
        bank.minutesUsedToday = minutesUsedToday;
        bank.updatedAt = new Date().toISOString();
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch time banks
      .addCase(fetchTimeBanks.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTimeBanks.fulfilled, (state, action) => {
        state.loading = false;
        state.timeBanks = action.payload;
      })
      .addCase(fetchTimeBanks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Fetch single time bank
      .addCase(fetchTimeBank.fulfilled, (state, action) => {
        const index = state.timeBanks.findIndex(bank => bank.userId === action.payload.userId);
        if (index !== -1) {
          state.timeBanks[index] = action.payload;
        } else {
          state.timeBanks.push(action.payload);
        }
      })
      
      // Add time
      .addCase(addTime.fulfilled, (state, action) => {
        const bank = state.timeBanks.find(b => b.userId === action.payload.userId);
        if (bank) {
          bank.minutesAvailable = action.payload.minutesAvailable;
          bank.updatedAt = action.payload.updatedAt;
        }
      })
      
      // Deduct time
      .addCase(deductTime.fulfilled, (state, action) => {
        const bank = state.timeBanks.find(b => b.userId === action.payload.userId);
        if (bank) {
          bank.minutesAvailable = action.payload.minutesAvailable;
          bank.minutesUsedToday = action.payload.minutesUsedToday;
          bank.updatedAt = action.payload.updatedAt;
        }
      })
      
      // Fetch usage stats
      .addCase(fetchUsageStats.fulfilled, (state, action) => {
        state.usageStats[action.payload.userId] = action.payload.stats;
      })
      
      // Reset daily usage
      .addCase(resetDailyUsage.fulfilled, (state) => {
        state.timeBanks.forEach(bank => {
          bank.minutesUsedToday = 0;
          bank.lastResetDate = new Date().toISOString().split('T')[0];
        });
      });
  },
});

export const { clearError, updateTimeBank } = timeSlice.actions;
export default timeSlice.reducer;

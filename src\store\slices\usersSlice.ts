import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

export interface User {
  userId: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'parent' | 'child';
  parentId?: string;
  isActive: boolean;
  createdAt: string;
}

interface UsersState {
  children: User[];
  loading: boolean;
  error: string | null;
}

const initialState: UsersState = {
  children: [],
  loading: false,
  error: null,
};

// Helper function to get auth headers
const getAuthHeaders = (getState: any) => {
  const token = getState().auth.token;
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// Async thunks
export const fetchChildren = createAsyncThunk(
  'users/fetchChildren',
  async (_, { getState, rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/users/children`, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch children');
    }
  }
);

export const createChild = createAsyncThunk(
  'users/createChild',
  async (childData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  }, { getState, rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/users/children`, {
        ...childData,
        role: 'child',
      }, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create child account');
    }
  }
);

export const updateChild = createAsyncThunk(
  'users/updateChild',
  async ({ userId, ...userData }: {
    userId: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    isActive?: boolean;
  }, { getState, rejectWithValue }) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/users/${userId}`, userData, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update child');
    }
  }
);

export const deleteChild = createAsyncThunk(
  'users/deleteChild',
  async (userId: string, { getState, rejectWithValue }) => {
    try {
      await axios.delete(`${API_BASE_URL}/users/${userId}`, {
        headers: getAuthHeaders(getState),
      });
      return userId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete child');
    }
  }
);

const usersSlice = createSlice({
  name: 'users',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch children
      .addCase(fetchChildren.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchChildren.fulfilled, (state, action) => {
        state.loading = false;
        state.children = action.payload;
      })
      .addCase(fetchChildren.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Create child
      .addCase(createChild.fulfilled, (state, action) => {
        state.children.push(action.payload);
      })
      
      // Update child
      .addCase(updateChild.fulfilled, (state, action) => {
        const index = state.children.findIndex(child => child.userId === action.payload.userId);
        if (index !== -1) {
          state.children[index] = action.payload;
        }
      })
      
      // Delete child
      .addCase(deleteChild.fulfilled, (state, action) => {
        state.children = state.children.filter(child => child.userId !== action.payload);
      });
  },
});

export const { clearError } = usersSlice.actions;
export default usersSlice.reducer;

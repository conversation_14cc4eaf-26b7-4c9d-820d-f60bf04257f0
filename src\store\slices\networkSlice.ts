import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

export interface NetworkSession {
  sessionId: string;
  userId: string;
  deviceId: string;
  startedAt: string;
  endedAt?: string;
  durationMinutes: number;
  bytesDownloaded: number;
  bytesUploaded: number;
  status: 'active' | 'expired' | 'terminated';
  // Populated fields
  deviceName?: string;
  userName?: string;
}

export interface NetworkStats {
  totalActiveSessions: number;
  totalDataUsage: number;
  averageSessionDuration: number;
  peakUsageTime: string;
}

interface NetworkState {
  activeSessions: NetworkSession[];
  sessionHistory: NetworkSession[];
  networkStats: NetworkStats | null;
  loading: boolean;
  error: string | null;
}

const initialState: NetworkState = {
  activeSessions: [],
  sessionHistory: [],
  networkStats: null,
  loading: false,
  error: null,
};

// Helper function to get auth headers
const getAuthHeaders = (getState: any) => {
  const token = getState().auth.token;
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// Async thunks
export const grantNetworkAccess = createAsyncThunk(
  'network/grantAccess',
  async ({ userId, deviceId, durationMinutes }: {
    userId: string;
    deviceId: string;
    durationMinutes: number;
  }, { getState, rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/network/grant-access`, {
        userId,
        deviceId,
        durationMinutes,
      }, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to grant network access');
    }
  }
);

export const revokeNetworkAccess = createAsyncThunk(
  'network/revokeAccess',
  async (sessionId: string, { getState, rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/network/revoke-access`, {
        sessionId,
      }, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to revoke network access');
    }
  }
);

export const fetchActiveSessions = createAsyncThunk(
  'network/fetchActiveSessions',
  async (_, { getState, rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/network/sessions/active`, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch active sessions');
    }
  }
);

export const fetchSessionHistory = createAsyncThunk(
  'network/fetchSessionHistory',
  async (params?: { userId?: string; startDate?: string; endDate?: string }, { getState, rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams();
      if (params?.userId) queryParams.append('userId', params.userId);
      if (params?.startDate) queryParams.append('startDate', params.startDate);
      if (params?.endDate) queryParams.append('endDate', params.endDate);

      const response = await axios.get(`${API_BASE_URL}/network/sessions/history?${queryParams}`, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch session history');
    }
  }
);

export const fetchNetworkStats = createAsyncThunk(
  'network/fetchNetworkStats',
  async (_, { getState, rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/network/stats`, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch network stats');
    }
  }
);

export const blockAllKidsAccess = createAsyncThunk(
  'network/blockAllKidsAccess',
  async (_, { getState, rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/network/block-all`, {}, {
        headers: getAuthHeaders(getState),
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to block all kids access');
    }
  }
);

const networkSlice = createSlice({
  name: 'network',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateSessionStatus: (state, action) => {
      const { sessionId, status } = action.payload;
      const session = state.activeSessions.find(s => s.sessionId === sessionId);
      if (session) {
        session.status = status;
        if (status === 'expired' || status === 'terminated') {
          // Move to history and remove from active
          state.sessionHistory.unshift(session);
          state.activeSessions = state.activeSessions.filter(s => s.sessionId !== sessionId);
        }
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Grant network access
      .addCase(grantNetworkAccess.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(grantNetworkAccess.fulfilled, (state, action) => {
        state.loading = false;
        state.activeSessions.push(action.payload);
      })
      .addCase(grantNetworkAccess.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Revoke network access
      .addCase(revokeNetworkAccess.fulfilled, (state, action) => {
        const sessionId = action.payload.sessionId;
        const sessionIndex = state.activeSessions.findIndex(s => s.sessionId === sessionId);
        if (sessionIndex !== -1) {
          const session = state.activeSessions[sessionIndex];
          session.status = 'terminated';
          session.endedAt = new Date().toISOString();
          state.sessionHistory.unshift(session);
          state.activeSessions.splice(sessionIndex, 1);
        }
      })
      
      // Fetch active sessions
      .addCase(fetchActiveSessions.fulfilled, (state, action) => {
        state.activeSessions = action.payload;
      })
      
      // Fetch session history
      .addCase(fetchSessionHistory.fulfilled, (state, action) => {
        state.sessionHistory = action.payload;
      })
      
      // Fetch network stats
      .addCase(fetchNetworkStats.fulfilled, (state, action) => {
        state.networkStats = action.payload;
      })
      
      // Block all kids access
      .addCase(blockAllKidsAccess.fulfilled, (state) => {
        // Mark all active sessions as terminated
        state.activeSessions.forEach(session => {
          session.status = 'terminated';
          session.endedAt = new Date().toISOString();
          state.sessionHistory.unshift(session);
        });
        state.activeSessions = [];
      });
  },
});

export const { clearError, updateSessionStatus } = networkSlice.actions;
export default networkSlice.reducer;

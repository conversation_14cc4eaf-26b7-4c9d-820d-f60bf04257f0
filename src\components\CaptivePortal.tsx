import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useData } from '../contexts/DataContext';
import { 
  Clock, 
  Wifi, 
  WifiOff, 
  CheckCircle, 
  AlertCircle,
  Star,
  Trophy,
  Gift,
  Timer,
  Zap
} from 'lucide-react';

export const CaptivePortal: React.FC = () => {
  const { user } = useAuth();
  const { childUsers, tasks, submitTask } = useData();
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState<string | null>(null);
  const [note, setNote] = useState('');

  const currentChild = childUsers.find(c => c.id === user?.id);

  useEffect(() => {
    if (currentChild) {
      setTimeRemaining(currentChild.timeBalance);
      setIsConnected(currentChild.isOnline && currentChild.timeBalance > 0);
    }
  }, [currentChild]);

  useEffect(() => {
    if (timeRemaining > 0 && isConnected) {
      const timer = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            setIsConnected(false);
            return 0;
          }
          return prev - 1;
        });
      }, 60000); // Update every minute

      return () => clearInterval(timer);
    }
  }, [timeRemaining, isConnected]);

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const handleTaskSubmit = () => {
    if (selectedTask && user?.id) {
      submitTask(selectedTask, user.id, undefined, note);
      setSelectedTask(null);
      setNote('');
      setShowTaskModal(false);
    }
  };

  const handleConnect = () => {
    if (timeRemaining > 0) {
      setIsConnected(true);
    }
  };

  const quickTasks = tasks.filter(task => task.rewardMinutes <= 30).slice(0, 3);

  if (!currentChild) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600">Please contact your parent to set up your account.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-4 rounded-full">
              <Gift className="w-12 h-12 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Hi, {user?.name}! 👋</h1>
          <p className="text-gray-600">Complete tasks to earn internet time</p>
        </div>

        {/* Main Status Card */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              {isConnected ? (
                <div className="bg-green-100 p-6 rounded-full">
                  <Wifi className="w-16 h-16 text-green-600" />
                </div>
              ) : (
                <div className="bg-gray-100 p-6 rounded-full">
                  <WifiOff className="w-16 h-16 text-gray-400" />
                </div>
              )}
            </div>

            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Internet Status</h2>
              <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${
                isConnected 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800'
              }`}>
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  isConnected ? 'bg-green-500' : 'bg-gray-400'
                }`} />
                {isConnected ? 'Connected' : 'Disconnected'}
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white mb-6">
              <div className="flex items-center justify-center mb-2">
                <Clock className="w-8 h-8 mr-2" />
                <span className="text-lg font-medium">Time Remaining</span>
              </div>
              <div className="text-4xl font-bold mb-2">{formatTime(timeRemaining)}</div>
              {timeRemaining > 0 && (
                <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
                  <div 
                    className="bg-white h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min((timeRemaining / 60) * 100, 100)}%` }}
                  />
                </div>
              )}
            </div>

            {timeRemaining > 0 && !isConnected && (
              <button
                onClick={handleConnect}
                className="bg-gradient-to-r from-green-600 to-blue-600 text-white px-8 py-3 rounded-xl font-semibold hover:from-green-700 hover:to-blue-700 transition-all duration-200 transform hover:scale-105 flex items-center mx-auto"
              >
                <Zap className="w-5 h-5 mr-2" />
                Connect to Internet
              </button>
            )}

            {timeRemaining === 0 && (
              <div className="text-center">
                <p className="text-gray-600 mb-4">No time remaining. Complete tasks to earn more!</p>
                <button
                  onClick={() => setShowTaskModal(true)}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-purple-700 hover:to-pink-700 transition-all duration-200 transform hover:scale-105"
                >
                  View Tasks
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-lg p-6 text-center">
            <div className="bg-yellow-100 p-3 rounded-full w-fit mx-auto mb-3">
              <Trophy className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">{currentChild.tasksCompleted}</div>
            <div className="text-sm text-gray-600">Tasks Completed</div>
          </div>
          
          <div className="bg-white rounded-xl shadow-lg p-6 text-center">
            <div className="bg-green-100 p-3 rounded-full w-fit mx-auto mb-3">
              <Timer className="w-6 h-6 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">{formatTime(currentChild.totalTimeEarned)}</div>
            <div className="text-sm text-gray-600">Total Time Earned</div>
          </div>
          
          <div className="bg-white rounded-xl shadow-lg p-6 text-center">
            <div className="bg-blue-100 p-3 rounded-full w-fit mx-auto mb-3">
              <Star className="w-6 h-6 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">{quickTasks.length}</div>
            <div className="text-sm text-gray-600">Quick Tasks Available</div>
          </div>
        </div>

        {/* Quick Tasks */}
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
            <Star className="w-6 h-6 text-yellow-500 mr-2" />
            Quick Tasks to Earn Time
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {quickTasks.map((task) => (
              <div key={task.id} className="border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow">
                <h4 className="font-semibold text-gray-900 mb-2">{task.title}</h4>
                <p className="text-sm text-gray-600 mb-3">{task.description}</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-green-600">
                    <Clock className="w-4 h-4 mr-1" />
                    <span className="font-bold">{task.rewardMinutes}m</span>
                  </div>
                  <button
                    onClick={() => {
                      setSelectedTask(task.id);
                      setShowTaskModal(true);
                    }}
                    className="bg-blue-600 text-white px-3 py-1 rounded-lg text-sm hover:bg-blue-700 transition-colors"
                  >
                    Do Task
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Task Submission Modal */}
        {showTaskModal && selectedTask && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-2xl max-w-md w-full p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Submit Task</h3>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tell us what you did:
                </label>
                <textarea
                  value={note}
                  onChange={(e) => setNote(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  placeholder="Describe how you completed the task..."
                />
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowTaskModal(false)}
                  className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleTaskSubmit}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Submit
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
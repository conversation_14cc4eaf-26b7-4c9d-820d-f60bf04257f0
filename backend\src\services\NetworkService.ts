import axios from 'axios';
import { DatabaseService } from './DatabaseService.js';

interface RouterConfig {
  host: string;
  username: string;
  password: string;
  port: number;
}

interface NetworkSession {
  sessionId: string;
  userId: string;
  deviceId: string;
  macAddress: string;
  startedAt: Date;
  durationMinutes: number;
  status: 'active' | 'expired' | 'terminated';
}

export class NetworkService {
  private static routerConfig: RouterConfig;
  private static activeSessions = new Map<string, NetworkSession>();
  private static sessionTimers = new Map<string, NodeJS.Timeout>();

  static async initialize(): Promise<void> {
    this.routerConfig = {
      host: process.env.ROUTER_HOST || '***********',
      username: process.env.ROUTER_USERNAME || 'admin',
      password: process.env.ROUTER_PASSWORD || 'admin',
      port: parseInt(process.env.ROUTER_API_PORT || '80'),
    };

    // Test router connection
    try {
      await this.testRouterConnection();
      console.log('✅ Router connection established');
    } catch (error) {
      console.warn('⚠️ Router connection failed, running in simulation mode');
    }
  }

  private static async testRouterConnection(): Promise<void> {
    const url = `http://${this.routerConfig.host}:${this.routerConfig.port}/cgi-bin/luci`;
    await axios.get(url, { timeout: 5000 });
  }

  // Grant internet access to a device for specified duration
  static async grantAccess(
    userId: string, 
    deviceId: string, 
    durationMinutes: number
  ): Promise<NetworkSession> {
    // Get device information
    const device = await DatabaseService.query(
      'SELECT mac_address FROM devices WHERE device_id = $1 AND user_id = $2',
      [deviceId, userId]
    );

    if (!device.rows[0]) {
      throw new Error('Device not found');
    }

    const macAddress = device.rows[0].mac_address;

    // Check if user has enough time
    const timeBank = await DatabaseService.getTimeBank(userId);
    if (!timeBank || timeBank.minutes_available < durationMinutes) {
      throw new Error('Insufficient time balance');
    }

    // Create network session
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const session: NetworkSession = {
      sessionId,
      userId,
      deviceId,
      macAddress,
      startedAt: new Date(),
      durationMinutes,
      status: 'active'
    };

    try {
      // Add firewall rule to allow access
      await this.addFirewallRule(macAddress, durationMinutes);

      // Store session in database
      await DatabaseService.query(`
        INSERT INTO network_sessions (session_id, user_id, device_id, started_at, duration_minutes, status)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [sessionId, userId, deviceId, session.startedAt, durationMinutes, 'active']);

      // Update time bank
      await DatabaseService.updateTimeBank(userId, durationMinutes);

      // Store active session
      this.activeSessions.set(sessionId, session);

      // Set timer to automatically revoke access
      const timer = setTimeout(() => {
        this.revokeAccess(sessionId);
      }, durationMinutes * 60 * 1000);

      this.sessionTimers.set(sessionId, timer);

      console.log(`✅ Access granted to ${macAddress} for ${durationMinutes} minutes`);
      return session;

    } catch (error) {
      console.error('❌ Failed to grant access:', error);
      throw new Error('Failed to grant network access');
    }
  }

  // Revoke internet access for a session
  static async revokeAccess(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.warn(`⚠️ Session ${sessionId} not found`);
      return;
    }

    try {
      // Remove firewall rule
      await this.removeFirewallRule(session.macAddress);

      // Update session status
      session.status = 'expired';
      const actualDuration = Math.floor((Date.now() - session.startedAt.getTime()) / (1000 * 60));

      await DatabaseService.query(`
        UPDATE network_sessions 
        SET status = $1, ended_at = CURRENT_TIMESTAMP, duration_minutes = $2
        WHERE session_id = $3
      `, ['expired', actualDuration, sessionId]);

      // Clean up
      this.activeSessions.delete(sessionId);
      const timer = this.sessionTimers.get(sessionId);
      if (timer) {
        clearTimeout(timer);
        this.sessionTimers.delete(sessionId);
      }

      console.log(`✅ Access revoked for ${session.macAddress}`);

    } catch (error) {
      console.error('❌ Failed to revoke access:', error);
    }
  }

  // Add firewall rule to allow device access
  private static async addFirewallRule(macAddress: string, durationMinutes: number): Promise<void> {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔧 [SIMULATION] Adding firewall rule for ${macAddress} (${durationMinutes} min)`);
      return;
    }

    try {
      // OpenWRT UCI command to add firewall rule
      const command = `
        uci add firewall rule
        uci set firewall.@rule[-1].name='Allow_${macAddress.replace(/:/g, '_')}'
        uci set firewall.@rule[-1].src='lan_kids'
        uci set firewall.@rule[-1].dest='wan'
        uci set firewall.@rule[-1].src_mac='${macAddress}'
        uci set firewall.@rule[-1].target='ACCEPT'
        uci set firewall.@rule[-1].enabled='1'
        uci commit firewall
        /etc/init.d/firewall reload
      `;

      await this.executeRouterCommand(command);
    } catch (error) {
      console.error('❌ Failed to add firewall rule:', error);
      throw error;
    }
  }

  // Remove firewall rule to block device access
  private static async removeFirewallRule(macAddress: string): Promise<void> {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔧 [SIMULATION] Removing firewall rule for ${macAddress}`);
      return;
    }

    try {
      const ruleName = `Allow_${macAddress.replace(/:/g, '_')}`;
      const command = `
        uci delete firewall.@rule[$(uci show firewall | grep "name='${ruleName}'" | cut -d'[' -f2 | cut -d']' -f1)]
        uci commit firewall
        /etc/init.d/firewall reload
      `;

      await this.executeRouterCommand(command);
    } catch (error) {
      console.error('❌ Failed to remove firewall rule:', error);
      throw error;
    }
  }

  // Execute command on OpenWRT router
  private static async executeRouterCommand(command: string): Promise<string> {
    const url = `http://${this.routerConfig.host}/cgi-bin/luci/admin/system/commands`;
    
    // Login first to get session
    const loginData = new URLSearchParams({
      luci_username: this.routerConfig.username,
      luci_password: this.routerConfig.password,
    });

    const loginResponse = await axios.post(
      `http://${this.routerConfig.host}/cgi-bin/luci`,
      loginData,
      { 
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        maxRedirects: 0,
        validateStatus: (status) => status < 400
      }
    );

    const cookies = loginResponse.headers['set-cookie'];
    
    // Execute command
    const commandData = new URLSearchParams({
      command: command
    });

    const response = await axios.post(url, commandData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cookie': cookies?.join('; ') || ''
      }
    });

    return response.data;
  }

  // Get active sessions for a user
  static getActiveSessionsForUser(userId: string): NetworkSession[] {
    return Array.from(this.activeSessions.values())
      .filter(session => session.userId === userId && session.status === 'active');
  }

  // Get all active sessions
  static getAllActiveSessions(): NetworkSession[] {
    return Array.from(this.activeSessions.values())
      .filter(session => session.status === 'active');
  }

  // Cleanup expired sessions (called by cron job)
  static async cleanupExpiredSessions(): Promise<void> {
    const expiredSessions = Array.from(this.activeSessions.values())
      .filter(session => {
        const elapsed = Date.now() - session.startedAt.getTime();
        return elapsed > (session.durationMinutes * 60 * 1000);
      });

    for (const session of expiredSessions) {
      await this.revokeAccess(session.sessionId);
    }

    console.log(`🧹 Cleaned up ${expiredSessions.length} expired sessions`);
  }

  // Emergency function to block all kids network access
  static async blockAllKidsAccess(): Promise<void> {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 [SIMULATION] Blocking all kids network access');
      return;
    }

    const command = `
      uci set firewall.kids_block=rule
      uci set firewall.kids_block.name='Block_All_Kids'
      uci set firewall.kids_block.src='lan_kids'
      uci set firewall.kids_block.dest='wan'
      uci set firewall.kids_block.target='REJECT'
      uci set firewall.kids_block.enabled='1'
      uci commit firewall
      /etc/init.d/firewall reload
    `;

    await this.executeRouterCommand(command);
    console.log('🚫 All kids network access blocked');
  }
}

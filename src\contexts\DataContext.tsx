import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface Task {
  id: string;
  title: string;
  description: string;
  rewardMinutes: number;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface TaskSubmission {
  id: string;
  taskId: string;
  childId: string;
  childName: string;
  taskTitle: string;
  rewardMinutes: number;
  submittedAt: Date;
  status: 'pending' | 'approved' | 'rejected';
  photoUrl?: string;
  note?: string;
  scheduledDate?: Date;
}

interface ScheduledTask {
  id: string;
  taskId: string;
  childId: string;
  scheduledDate: Date;
  completed: boolean;
  submissionId?: string;
}

interface Child {
  id: string;
  name: string;
  email: string;
  timeBalance: number;
  isOnline: boolean;
  devices: Device[];
  totalTimeEarned: number;
  tasksCompleted: number;
}

interface Device {
  id: string;
  name: string;
  macAddress: string;
  type: 'phone' | 'tablet' | 'laptop' | 'desktop';
  isConnected: boolean;
  lastSeen: Date;
}

interface DataContextType {
  tasks: Task[];
  submissions: TaskSubmission[];
  childUsers: Child[];
  scheduledTasks: ScheduledTask[];
  addTask: (task: Omit<Task, 'id'>) => void;
  submitTask: (taskId: string, childId: string, photoUrl?: string, note?: string) => void;
  approveSubmission: (submissionId: string) => void;
  rejectSubmission: (submissionId: string) => void;
  updateTimeBalance: (childId: string, minutes: number) => void;
  scheduleTask: (taskId: string, childId: string, date: Date) => void;
  getTasksForDate: (date: Date) => ScheduledTask[];
  getTasksForChild: (childId: string, date: Date) => ScheduledTask[];
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export const DataProvider: React.FC = ({ children }) => {
  const [tasks, setTasks] = useState<Task[]>([
    {
      id: '1',
      title: 'Make Bed',
      description: 'Make your bed neatly with pillows arranged',
      rewardMinutes: 15,
      category: 'Bedroom',
      difficulty: 'easy'
    },
    {
      id: '2',
      title: 'Do Homework',
      description: 'Complete all assigned homework and show completed work',
      rewardMinutes: 45,
      category: 'Education',
      difficulty: 'medium'
    },
    {
      id: '3',
      title: 'Clean Room',
      description: 'Organize and clean your entire room',
      rewardMinutes: 30,
      category: 'Bedroom',
      difficulty: 'medium'
    },
    {
      id: '4',
      title: 'Take Out Trash',
      description: 'Empty all trash bins and take to curb',
      rewardMinutes: 10,
      category: 'Household',
      difficulty: 'easy'
    },
    {
      id: '5',
      title: 'Wash Dishes',
      description: 'Wash, dry, and put away all dishes',
      rewardMinutes: 20,
      category: 'Kitchen',
      difficulty: 'medium'
    }
  ]);

  const [submissions, setSubmissions] = useState<TaskSubmission[]>([
    {
      id: '1',
      taskId: '1',
      childId: '2',
      childName: 'Tommy',
      taskTitle: 'Make Bed',
      rewardMinutes: 15,
      submittedAt: new Date(),
      status: 'pending',
      photoUrl: 'https://images.pexels.com/photos/1454806/pexels-photo-1454806.jpeg?auto=compress&cs=tinysrgb&w=400',
      note: 'Made my bed with fresh sheets!'
    }
  ]);

  const [scheduledTasks, setScheduledTasks] = useState<ScheduledTask[]>([
    {
      id: '1',
      taskId: '1',
      childId: '2',
      scheduledDate: new Date(),
      completed: false
    },
    {
      id: '2',
      taskId: '2',
      childId: '2',
      scheduledDate: new Date(Date.now() + 86400000), // Tomorrow
      completed: false
    },
    {
      id: '3',
      taskId: '3',
      childId: '3',
      scheduledDate: new Date(),
      completed: false
    }
  ]);

  const [childUsers, setChildUsers] = useState<Child[]>([
    {
      id: '2',
      name: 'Tommy',
      email: '<EMAIL>',
      timeBalance: 45,
      isOnline: true,
      totalTimeEarned: 120,
      tasksCompleted: 8,
      devices: [
        {
          id: '1',
          name: 'Tommy\'s iPhone',
          macAddress: '00:1B:44:11:3A:B7',
          type: 'phone',
          isConnected: true,
          lastSeen: new Date()
        },
        {
          id: '2',
          name: 'Tommy\'s iPad',
          macAddress: '00:1B:44:11:3A:B8',
          type: 'tablet',
          isConnected: false,
          lastSeen: new Date(Date.now() - 3600000)
        }
      ]
    },
    {
      id: '3',
      name: 'Emma',
      email: '<EMAIL>',
      timeBalance: 0,
      isOnline: false,
      totalTimeEarned: 90,
      tasksCompleted: 6,
      devices: [
        {
          id: '3',
          name: 'Emma\'s Laptop',
          macAddress: '00:1B:44:11:3A:B9',
          type: 'laptop',
          isConnected: false,
          lastSeen: new Date(Date.now() - 7200000)
        }
      ]
    }
  ]);

  const addTask = (task: Omit<Task, 'id'>) => {
    const newTask = {
      ...task,
      id: Date.now().toString()
    };
    setTasks(prev => [...prev, newTask]);
  };

  const submitTask = (taskId: string, childId: string, photoUrl?: string, note?: string) => {
    const task = tasks.find(t => t.id === taskId);
    const child = childUsers.find(c => c.id === childId);
    
    if (task && child) {
      const newSubmission: TaskSubmission = {
        id: Date.now().toString(),
        taskId,
        childId,
        childName: child.name,
        taskTitle: task.title,
        rewardMinutes: task.rewardMinutes,
        submittedAt: new Date(),
        status: 'pending',
        photoUrl,
        note
      };
      setSubmissions(prev => [...prev, newSubmission]);

      // Mark scheduled task as completed if it exists
      setScheduledTasks(prev => prev.map(st => {
        if (st.taskId === taskId && st.childId === childId && !st.completed) {
          const today = new Date();
          const scheduledDate = new Date(st.scheduledDate);
          if (today.toDateString() === scheduledDate.toDateString()) {
            return { ...st, completed: true, submissionId: newSubmission.id };
          }
        }
        return st;
      }));
    }
  };

  const approveSubmission = (submissionId: string) => {
    setSubmissions(prev => prev.map(sub => 
      sub.id === submissionId 
        ? { ...sub, status: 'approved' as const }
        : sub
    ));

    const submission = submissions.find(s => s.id === submissionId);
    if (submission) {
      updateTimeBalance(submission.childId, submission.rewardMinutes);
      setChildUsers(prev => prev.map(child => 
        child.id === submission.childId 
          ? { ...child, tasksCompleted: child.tasksCompleted + 1, totalTimeEarned: child.totalTimeEarned + submission.rewardMinutes }
          : child
      ));
    }
  };

  const rejectSubmission = (submissionId: string) => {
    setSubmissions(prev => prev.map(sub => 
      sub.id === submissionId 
        ? { ...sub, status: 'rejected' as const }
        : sub
    ));
  };

  const updateTimeBalance = (childId: string, minutes: number) => {
    setChildUsers(prev => prev.map(child => 
      child.id === childId 
        ? { ...child, timeBalance: Math.max(0, child.timeBalance + minutes) }
        : child
    ));
  };

  const scheduleTask = (taskId: string, childId: string, date: Date) => {
    const newScheduledTask: ScheduledTask = {
      id: Date.now().toString(),
      taskId,
      childId,
      scheduledDate: date,
      completed: false
    };
    setScheduledTasks(prev => [...prev, newScheduledTask]);
  };

  const getTasksForDate = (date: Date) => {
    return scheduledTasks.filter(st => {
      const scheduledDate = new Date(st.scheduledDate);
      return scheduledDate.toDateString() === date.toDateString();
    });
  };

  const getTasksForChild = (childId: string, date: Date) => {
    return scheduledTasks.filter(st => {
      const scheduledDate = new Date(st.scheduledDate);
      return st.childId === childId && scheduledDate.toDateString() === date.toDateString();
    });
  };

  return (
    <DataContext.Provider value={{
      tasks,
      submissions,
      childUsers,
      scheduledTasks,
      addTask,
      submitTask,
      approveSubmission,
      rejectSubmission,
      updateTimeBalance,
      scheduleTask,
      getTasksForDate,
      getTasksForChild
    }}>
      {children}
    </DataContext.Provider>
  );
};
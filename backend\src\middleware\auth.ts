import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { DatabaseService } from '../services/DatabaseService.js';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        userId: string;
        email: string;
        role: 'parent' | 'child';
        firstName: string;
        lastName: string;
        parentId?: string;
      };
    }
  }
}

export async function authenticateToken(req: Request, res: Response, next: NextFunction) {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'No token provided'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;

    // Get fresh user data to ensure user still exists and is active
    const user = await DatabaseService.getUserById(decoded.userId);
    if (!user) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'Invalid token - user not found'
      });
    }

    // Add user to request object
    req.user = {
      userId: user.user_id,
      email: user.email,
      role: user.role,
      firstName: user.first_name,
      lastName: user.last_name,
      parentId: user.parent_id,
    };

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'Invalid token'
      });
    }

    console.error('Authentication error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Authentication failed'
    });
  }
}

export function requireRole(roles: ('parent' | 'child')[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'Authentication required'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        error: 'Access denied',
        message: `This endpoint requires ${roles.join(' or ')} role`
      });
    }

    next();
  };
}

export function requireParentRole(req: Request, res: Response, next: NextFunction) {
  return requireRole(['parent'])(req, res, next);
}

export function requireChildRole(req: Request, res: Response, next: NextFunction) {
  return requireRole(['child'])(req, res, next);
}

// Middleware to ensure parent can only access their own children's data
export async function validateParentChildRelationship(req: Request, res: Response, next: NextFunction) {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Access denied',
        message: 'Authentication required'
      });
    }

    // If user is a parent, they can access any child data (we'll validate ownership in routes)
    if (req.user.role === 'parent') {
      return next();
    }

    // If user is a child, they can only access their own data
    const targetUserId = req.params.userId || req.body.userId || req.query.userId;
    
    if (targetUserId && targetUserId !== req.user.userId) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You can only access your own data'
      });
    }

    next();
  } catch (error) {
    console.error('Parent-child relationship validation error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Access validation failed'
    });
  }
}

// Middleware to validate that a parent can only manage their own children
export async function validateChildOwnership(req: Request, res: Response, next: NextFunction) {
  try {
    if (!req.user || req.user.role !== 'parent') {
      return res.status(403).json({
        error: 'Access denied',
        message: 'Parent role required'
      });
    }

    const childUserId = req.params.userId || req.body.userId || req.params.childId;
    
    if (!childUserId) {
      return next(); // No child ID to validate
    }

    // Check if the child belongs to this parent
    const child = await DatabaseService.getUserById(childUserId);
    
    if (!child) {
      return res.status(404).json({
        error: 'Child not found',
        message: 'The specified child does not exist'
      });
    }

    if (child.parent_id !== req.user.userId) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You can only manage your own children'
      });
    }

    next();
  } catch (error) {
    console.error('Child ownership validation error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Ownership validation failed'
    });
  }
}
